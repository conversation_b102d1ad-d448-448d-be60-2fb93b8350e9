# 重复监听直播间问题修复

## 问题描述

在 `/home/<USER>/WebstormProjects/tiktokTools/src/` 目录中存在重复监听相同直播间的问题。当多次请求监听同一个直播间URL时，系统会创建多个连接，导致资源浪费和潜在的冲突。

## 问题原因

1. **缺少重复检查机制**：在创建新连接时没有检查是否已经存在相同URL的连接
2. **连接存储方式**：连接以随机生成的 `connectionId` 为键存储，无法快速检查URL重复
3. **多个入口点**：`handleProtobufLiveRoomRequest` 和 `startMonitorRoom` 都可以创建连接，但都缺少重复检查

## 修复方案

### 1. 客户端修复 (`src/live-websocket-bridge.js`)

#### 添加URL映射跟踪
```javascript
// 添加URL到连接ID的映射
this.urlToConnectionId = new Map(); // liveUrl -> connectionId (用于检查重复)
```

#### 修复连接创建逻辑
在以下方法中添加重复检查：

- `handleProtobufLiveRoomRequest()` - 处理服务器请求时检查重复
- `startMonitorRoom()` - 直接启动监听时检查重复
- `createLiveConnection()` - 建立URL映射
- `disconnectConnection()` - 清理URL映射

#### 添加辅助方法
```javascript
// 获取所有活跃的直播间URL列表
getActiveLiveUrls()

// 检查指定URL是否正在被监听
isLiveUrlActive(liveUrl)
```

### 2. 服务器端修复 (`src/remote-server-demo.js`)

#### 添加客户端URL映射跟踪
```javascript
// 添加客户端URL到连接ID的映射
this.clientUrlConnections = new Map(); // clientId_liveUrl -> connectionId (用于检查重复)
```

#### 修复请求处理逻辑
- `requestLiveRoom()` - 请求连接前检查重复
- `handleLiveRoomResponse()` - 建立客户端URL映射
- `handleDisconnectRoomResponse()` - 清理客户端URL映射

#### 添加辅助方法
```javascript
// 检查客户端是否已经在监听指定URL
isClientMonitoringUrl(clientId, liveUrl)

// 获取客户端正在监听的所有URL
getClientMonitoringUrls(clientId)
```

## 修复效果

### 修复前
- 同一个直播间URL可能被多次监听
- 创建多个不必要的连接和窗口
- 资源浪费和潜在冲突

### 修复后
- 自动检测重复的直播间URL
- 复用现有连接，避免重复创建
- 正确清理断开的连接映射
- 提供查询和管理接口

## 测试验证

创建了测试脚本 `src/test-duplicate-fix.js` 来验证修复效果：

```bash
node src/test-duplicate-fix.js
```

测试场景包括：
1. 直接调用 `createLiveConnection` 的重复检查
2. 模拟服务器请求的重复检查  
3. 断开连接后的映射清理

## 使用示例

### 客户端检查重复
```javascript
const bridge = new LiveWebSocketBridge();

// 检查URL是否已被监听
if (bridge.isLiveUrlActive(liveUrl)) {
    console.log('该直播间已在监听中');
    return;
}

// 获取所有活跃URL
const activeUrls = bridge.getActiveLiveUrls();
console.log('当前监听的直播间:', activeUrls);
```

### 服务器端检查重复
```javascript
const server = new RemoteWebSocketServer();

// 检查客户端是否已在监听指定URL
if (server.isClientMonitoringUrl(clientId, liveUrl)) {
    console.log('客户端已在监听该直播间');
    return;
}

// 获取客户端监听的所有URL
const urls = server.getClientMonitoringUrls(clientId);
console.log('客户端监听的直播间:', urls);
```

## 注意事项

1. **向后兼容**：修复保持了现有API的兼容性
2. **内存管理**：正确清理映射关系，避免内存泄漏
3. **并发安全**：使用Map数据结构确保操作的原子性
4. **日志记录**：添加了详细的日志来跟踪重复检查过程

## 相关文件

- `src/live-websocket-bridge.js` - 客户端连接管理
- `src/remote-server-demo.js` - 服务器端连接管理
- `src/test-duplicate-fix.js` - 测试脚本
- `DUPLICATE_MONITORING_FIX.md` - 本文档
