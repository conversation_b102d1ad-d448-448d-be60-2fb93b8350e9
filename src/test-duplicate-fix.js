#!/usr/bin/env node

/**
 * 测试重复监听直播间问题的修复
 * 这个脚本会模拟多次请求监听同一个直播间，验证是否正确处理重复请求
 */

const  LiveWebSocketBridge  = require('./live-websocket-bridge');

async function testDuplicateConnectionFix() {
    console.log('🧪 开始测试重复监听直播间问题的修复...\n');

    // 创建LiveWebSocketBridge实例
    const bridge = new LiveWebSocketBridge('test-context', 8085, true);

    const testUrl = 'https://www.tiktok.com/@test/live';

    console.log('📋 测试场景 1: 直接调用 createLiveConnection');
    console.log('=' .repeat(50));

    try {
        // 第一次创建连接
        console.log('1️⃣ 第一次创建连接...');
        const connection1 = await bridge.createLiveConnection('test-conn-1', testUrl);
        console.log(`✅ 连接1创建成功: ${connection1.id}`);

        // 检查活跃URL列表
        const activeUrls1 = bridge.getActiveLiveUrls();
        console.log(`📊 当前活跃URL数量: ${activeUrls1.length}`);
        console.log(`📊 活跃URLs: ${JSON.stringify(activeUrls1)}`);

        // 尝试创建相同URL的第二个连接
        console.log('\n2️⃣ 尝试创建相同URL的第二个连接...');
        const connection2 = await bridge.createLiveConnection('test-conn-2', testUrl);
        console.log(`✅ 连接2创建成功: ${connection2.id}`);

        // 再次检查活跃URL列表
        const activeUrls2 = bridge.getActiveLiveUrls();
        console.log(`📊 当前活跃URL数量: ${activeUrls2.length}`);
        console.log(`📊 活跃URLs: ${JSON.stringify(activeUrls2)}`);

        // 验证是否正确处理重复
        if (activeUrls2.length === 1) {
            console.log('✅ 重复检查正常工作 - 只有一个活跃URL');
        } else {
            console.log('❌ 重复检查失败 - 存在重复的活跃URL');
        }

        // 检查连接状态
        console.log(`\n📊 连接状态检查:`);
        console.log(`- 连接1是否活跃: ${bridge.connections.has(connection1.id)}`);
        console.log(`- 连接2是否活跃: ${bridge.connections.has(connection2.id)}`);
        console.log(`- URL是否被监听: ${bridge.isLiveUrlActive(testUrl)}`);

    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error);
    }

    console.log('\n📋 测试场景 2: 模拟服务器请求');
    console.log('=' .repeat(50));

    try {
        // 模拟第一个服务器请求
        console.log('1️⃣ 模拟第一个服务器请求...');
        const mockMessage1 = {
            liveUrl: 'https://www.tiktok.com/@test2/live',
            options: '{}',
            requestId: 'req-1'
        };
        await bridge.handleProtobufLiveRoomRequest(mockMessage1);

        // 检查状态
        const activeUrls3 = bridge.getActiveLiveUrls();
        console.log(`📊 第一次请求后活跃URL数量: ${activeUrls3.length}`);

        // 模拟相同URL的第二个服务器请求
        console.log('\n2️⃣ 模拟相同URL的第二个服务器请求...');
        const mockMessage2 = {
            liveUrl: 'https://www.tiktok.com/@test2/live',
            options: '{}',
            requestId: 'req-2'
        };
        await bridge.handleProtobufLiveRoomRequest(mockMessage2);

        // 再次检查状态
        const activeUrls4 = bridge.getActiveLiveUrls();
        console.log(`📊 第二次请求后活跃URL数量: ${activeUrls4.length}`);

        // 验证结果
        if (activeUrls4.length === activeUrls3.length) {
            console.log('✅ 服务器请求重复检查正常工作');
        } else {
            console.log('❌ 服务器请求重复检查失败');
        }

    } catch (error) {
        console.error('❌ 服务器请求测试中发生错误:', error);
    }

    console.log('\n📋 测试场景 3: 断开连接后的清理');
    console.log('=' .repeat(50));

    try {
        const testUrl3 = 'https://www.tiktok.com/@test3/live';
        
        // 创建连接
        console.log('1️⃣ 创建测试连接...');
        const connection3 = await bridge.createLiveConnection('test-conn-3', testUrl3);
        console.log(`✅ 连接创建成功: ${connection3.id}`);

        // 检查映射
        console.log(`📊 URL映射存在: ${bridge.urlToConnectionId.has(testUrl3)}`);
        console.log(`📊 连接存在: ${bridge.connections.has(connection3.id)}`);

        // 断开连接
        console.log('\n2️⃣ 断开连接...');
        const disconnected = bridge.disconnectConnection(connection3.id);
        console.log(`✅ 连接断开: ${disconnected}`);

        // 检查清理情况
        console.log(`📊 URL映射已清理: ${!bridge.urlToConnectionId.has(testUrl3)}`);
        console.log(`📊 连接已清理: ${!bridge.connections.has(connection3.id)}`);

        if (!bridge.urlToConnectionId.has(testUrl3) && !bridge.connections.has(connection3.id)) {
            console.log('✅ 断开连接后清理正常');
        } else {
            console.log('❌ 断开连接后清理失败');
        }

    } catch (error) {
        console.error('❌ 断开连接测试中发生错误:', error);
    }

    console.log('\n🎯 测试总结');
    console.log('=' .repeat(50));
    console.log('✅ 已修复的问题:');
    console.log('  - 添加了 urlToConnectionId 映射来跟踪URL到连接ID的关系');
    console.log('  - 在创建连接前检查是否已存在相同URL的连接');
    console.log('  - 在断开连接时正确清理URL映射');
    console.log('  - 提供了辅助方法来查询活跃URL和检查重复');
    console.log('\n🔧 修复位置:');
    console.log('  - src/live-websocket-bridge.js: handleProtobufLiveRoomRequest()');
    console.log('  - src/live-websocket-bridge.js: createLiveConnection()');
    console.log('  - src/live-websocket-bridge.js: disconnectConnection()');
    console.log('  - src/live-websocket-bridge.js: startMonitorRoom()');
    console.log('  - src/remote-server-demo.js: requestLiveRoom()');

    // 清理所有连接
    console.log('\n🧹 清理测试连接...');
    const disconnectedCount = bridge.disconnectAllConnections();
    console.log(`✅ 已清理 ${disconnectedCount} 个连接`);
}

// 运行测试
if (require.main === module) {
    testDuplicateConnectionFix().catch(console.error);
}

module.exports = { testDuplicateConnectionFix };
